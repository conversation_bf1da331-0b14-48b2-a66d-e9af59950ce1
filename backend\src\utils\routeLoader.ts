import { Express, Router } from 'express';
import { FeatureRoutes, FeatureModule } from '../types/routes.types';
import fs from 'fs';
import path from 'path';

/**
 * Dynamically load and register routes from feature directories
 */
export class RouteLoader {
    private app: Express;
    private routesPath: string;
    private loadedModules: FeatureModule[] = [];

    constructor(app: Express, routesPath: string = path.join(__dirname, '../routes')) {
        this.app = app;
        this.routesPath = routesPath;
    }

    /**
     * Load all route modules from the routes directory
     */
    public async loadRoutes(): Promise<void> {
        try {
            console.log('🔄 Loading routes from:', this.routesPath);

            if (!fs.existsSync(this.routesPath)) {
                console.warn('⚠️  Routes directory not found, creating it...');
                fs.mkdirSync(this.routesPath, { recursive: true });
                return;
            }

            const featureDirectories = this.getFeatureDirectories();
            
            for (const featureDir of featureDirectories) {
                await this.loadFeatureRoutes(featureDir);
            }

            this.logLoadedRoutes();
        } catch (error) {
            console.error('❌ Error loading routes:', error);
            throw error;
        }
    }

    /**
     * Get all feature directories in the routes folder
     */
    private getFeatureDirectories(): string[] {
        const items = fs.readdirSync(this.routesPath);
        return items.filter(item => {
            const itemPath = path.join(this.routesPath, item);
            return fs.statSync(itemPath).isDirectory();
        });
    }

    /**
     * Load routes for a specific feature
     */
    private async loadFeatureRoutes(featureName: string): Promise<void> {
        const featurePath = path.join(this.routesPath, featureName);
        const routeFiles = this.getRouteFiles(featurePath);

        for (const routeFile of routeFiles) {
            await this.loadRouteFile(featureName, routeFile);
        }
    }

    /**
     * Get all route files in a feature directory
     */
    private getRouteFiles(featurePath: string): string[] {
        const files = fs.readdirSync(featurePath);
        return files.filter(file => 
            file.endsWith('.routes.ts') || file.endsWith('.routes.js')
        );
    }

    /**
     * Load a specific route file
     */
    private async loadRouteFile(featureName: string, routeFile: string): Promise<void> {
        try {
            const routeFilePath = path.join(this.routesPath, featureName, routeFile);
            
            // Dynamic import for TypeScript files
            const routeModule = await import(routeFilePath);
            
            if (routeModule.default && typeof routeModule.default === 'object') {
                const featureRoutes: FeatureRoutes = routeModule.default;
                this.registerFeatureRoutes(featureName, featureRoutes);
            } else {
                console.warn(`⚠️  Invalid route module format in ${routeFile}`);
            }
        } catch (error) {
            console.error(`❌ Error loading route file ${routeFile}:`, error);
        }
    }

    /**
     * Register routes for a feature
     */
    private registerFeatureRoutes(featureName: string, featureRoutes: FeatureRoutes): void {
        const { config, setup } = featureRoutes;
        const router = setup();

        // Apply feature-specific middleware if any
        if (config.middleware && config.middleware.length > 0) {
            config.middleware.forEach(middleware => {
                this.app.use(config.path, middleware);
            });
        }

        // Register the router
        this.app.use(config.path, router);

        // Track loaded module
        this.loadedModules.push({
            name: featureName,
            basePath: config.path,
            routes: [], // Will be populated by route metadata if needed
            middleware: config.middleware?.map(m => m.name) || []
        });

        console.log(`✅ Loaded ${featureName} routes at ${config.path}`);
    }

    /**
     * Log summary of loaded routes
     */
    private logLoadedRoutes(): void {
        console.log('\n📋 Route Loading Summary:');
        console.log('========================');
        
        if (this.loadedModules.length === 0) {
            console.log('No routes loaded');
            return;
        }

        this.loadedModules.forEach(module => {
            console.log(`🎯 ${module.name}: ${module.basePath}`);
            if (module.middleware && module.middleware.length > 0) {
                console.log(`   Middleware: ${module.middleware.join(', ')}`);
            }
        });
        
        console.log(`\n✨ Total features loaded: ${this.loadedModules.length}`);
        console.log('========================\n');
    }

    /**
     * Get loaded modules information
     */
    public getLoadedModules(): FeatureModule[] {
        return this.loadedModules;
    }

    /**
     * Get routes documentation
     */
    public getRoutesDocumentation(): any {
        return {
            totalFeatures: this.loadedModules.length,
            features: this.loadedModules.map(module => ({
                name: module.name,
                basePath: module.basePath,
                middleware: module.middleware
            }))
        };
    }
}
