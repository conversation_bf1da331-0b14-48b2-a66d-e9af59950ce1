{"name": "backend", "version": "1.0.0", "main": "index.js", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mysql2": "^3.14.1", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.24"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.28", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "scripts": {"dev": "ts-node-dev --respawn src/index.ts"}}